class QuantityDiscount {
  constructor() {
    this.init();
  }

  init() {
    this.bindEvents();
  }

  bindEvents() {
    document.addEventListener('click', (e) => {
      if (e.target.matches('.quantity-discount__button')) {
        e.preventDefault();
        this.handleDiscountSelection(e.target);
      }
    });

    // Listen for variant changes
    document.addEventListener('change', (e) => {
      if (e.target.matches('input[name="id"]') || e.target.matches('select[name="id"]')) {
        this.updateDiscountPrices();
      }
    });
  }

  async handleDiscountSelection(button) {
    const quantity = parseInt(button.dataset.quantity);
    const variantId = button.dataset.variantId;
    
    if (!quantity || !variantId) {
      console.error('Missing quantity or variant ID');
      return;
    }

    // Update button state
    this.setButtonLoading(button, true);

    try {
      // Add to cart
      const response = await this.addToCart(variantId, quantity);
      
      if (response.ok) {
        const data = await response.json();
        this.handleAddToCartSuccess(button, data);
      } else {
        throw new Error('Failed to add to cart');
      }
    } catch (error) {
      console.error('Error adding to cart:', error);
      this.handleAddToCartError(button, error);
    }
  }

  async addToCart(variantId, quantity) {
    const formData = new FormData();
    formData.append('id', variantId);
    formData.append('quantity', quantity);

    // Add sections to update cart drawer/notification
    const cart = document.querySelector('cart-notification') || document.querySelector('cart-drawer');
    if (cart) {
      const sections = cart.getSectionsToRender().map(section => section.id);
      formData.append('sections', sections);
      formData.append('sections_url', window.location.pathname);
    }

    return fetch(window.Shopify.routes.root + 'cart/add.js', {
      method: 'POST',
      headers: {
        'X-Requested-With': 'XMLHttpRequest'
      },
      body: formData
    });
  }

  handleAddToCartSuccess(button, data) {
    // Show success state
    this.setButtonSuccess(button);
    
    // Update cart UI
    this.updateCartUI(data);
    
    // Reset button after delay
    setTimeout(() => {
      this.resetButton(button);
    }, 2000);
  }

  handleAddToCartError(button, error) {
    // Reset button
    this.setButtonLoading(button, false);
    
    // Show error message
    this.showErrorMessage('Ürün sepete eklenirken bir hata oluştu. Lütfen tekrar deneyin.');
  }

  setButtonLoading(button, loading) {
    if (loading) {
      button.classList.add('loading');
      button.disabled = true;
      const originalText = button.innerHTML;
      button.dataset.originalText = originalText;
      button.innerHTML = '<span class="loading__spinner"></span>Ekleniyor...';
    } else {
      button.classList.remove('loading');
      button.disabled = false;
      if (button.dataset.originalText) {
        button.innerHTML = button.dataset.originalText;
      }
    }
  }

  setButtonSuccess(button) {
    button.classList.remove('loading');
    button.classList.add('success');
    button.disabled = false;
    button.innerHTML = '✓ Eklendi';
  }

  resetButton(button) {
    button.classList.remove('loading', 'success');
    button.disabled = false;
    if (button.dataset.originalText) {
      button.innerHTML = button.dataset.originalText;
    }
  }

  updateCartUI(data) {
    // Update cart notification
    const cartNotification = document.querySelector('cart-notification');
    if (cartNotification) {
      cartNotification.renderContents(data);
    }

    // Update cart drawer
    const cartDrawer = document.querySelector('cart-drawer');
    if (cartDrawer) {
      cartDrawer.renderContents(data);
    }

    // Update cart count
    const cartCountElements = document.querySelectorAll('.cart-count-bubble');
    cartCountElements.forEach(element => {
      if (data.item_count !== undefined) {
        element.textContent = data.item_count;
        element.classList.toggle('hidden', data.item_count === 0);
      }
    });
  }

  updateDiscountPrices() {
    // Get current variant
    const variantInput = document.querySelector('input[name="id"], select[name="id"]');
    if (!variantInput) return;

    const currentVariantId = variantInput.value;
    
    // Update all discount options with new variant ID
    const discountOptions = document.querySelectorAll('.quantity-discount__option');
    discountOptions.forEach(option => {
      option.dataset.variantId = currentVariantId;
      const button = option.querySelector('.quantity-discount__button');
      if (button) {
        button.dataset.variantId = currentVariantId;
      }
    });

    // Recalculate prices if needed
    this.recalculatePrices();
  }

  recalculatePrices() {
    // Get current variant price from data attribute or price element
    let currentPrice = 0;

    // Try to get price from variant data first
    const variantInput = document.querySelector('input[name="id"], select[name="id"]');
    if (variantInput && window.productVariants) {
      const variantId = variantInput.value;
      const variant = window.productVariants.find(v => v.id == variantId);
      if (variant) {
        currentPrice = variant.price; // Already in cents
      }
    }

    // Fallback to price element
    if (!currentPrice) {
      const priceElement = document.querySelector('.price__regular .money');
      if (!priceElement) return;

      // Extract price more carefully
      const priceText = priceElement.textContent || priceElement.innerText;
      const cleanPrice = priceText.replace(/[^\d,\.]/g, '');

      // Handle Turkish format (1.200,50) vs English format (1,200.50)
      let numericPrice;
      if (cleanPrice.includes(',') && cleanPrice.includes('.')) {
        // Turkish format: 1.200,50
        numericPrice = parseFloat(cleanPrice.replace(/\./g, '').replace(',', '.'));
      } else if (cleanPrice.includes(',')) {
        // Could be Turkish decimal: 200,50 or thousands: 1,200
        const parts = cleanPrice.split(',');
        if (parts[1] && parts[1].length <= 2) {
          // Decimal: 200,50
          numericPrice = parseFloat(cleanPrice.replace(',', '.'));
        } else {
          // Thousands: 1,200
          numericPrice = parseFloat(cleanPrice.replace(',', ''));
        }
      } else {
        // Simple number
        numericPrice = parseFloat(cleanPrice);
      }

      currentPrice = Math.round(numericPrice * 100); // Convert to cents
    }

    // Update each discount option
    const discountOptions = document.querySelectorAll('.quantity-discount__option');
    discountOptions.forEach(option => {
      const quantity = parseInt(option.dataset.quantity);
      const discountPrice = parseInt(option.dataset.price); // Should be in cents from Liquid
      const discountPriceTL = parseFloat(option.dataset.priceTl); // TL value for verification

      console.log('Debug:', {
        quantity,
        discountPrice,
        discountPriceTL,
        currentPrice,
        discountPriceInTL: discountPrice / 100,
        shouldBe: discountPriceTL
      });
      const totalRegularPrice = currentPrice * quantity;
      const savings = totalRegularPrice - discountPrice;
      const savingsPercentage = savings > 0 ? Math.round((savings / totalRegularPrice) * 100) : 0;

      // Update data attributes
      option.dataset.regularPrice = totalRegularPrice;
      option.dataset.savings = savings;
      option.dataset.savingsPercentage = savingsPercentage;

      // Update savings display
      const savingsElement = option.querySelector('.quantity-discount__savings');
      if (savingsElement && savings > 0) {
        savingsElement.textContent = this.formatMoney(savings) + ' tasarruf';
      }

      // Update regular price display
      const regularPriceElement = option.querySelector('.quantity-discount__regular-price');
      if (regularPriceElement) {
        regularPriceElement.textContent = this.formatMoney(totalRegularPrice);
      }

      // Update percentage display
      const percentageElement = option.querySelector('.quantity-discount__percentage');
      if (percentageElement && savingsPercentage > 0) {
        percentageElement.textContent = ` (%${savingsPercentage} İndirim)`;
      }
    });
  }

  formatMoney(cents) {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY'
    }).format(cents / 100);
  }

  showErrorMessage(message) {
    // Create or update error message
    let errorDiv = document.querySelector('.quantity-discount-error');
    if (!errorDiv) {
      errorDiv = document.createElement('div');
      errorDiv.className = 'quantity-discount-error';
      errorDiv.style.cssText = `
        background: #f8d7da;
        color: #721c24;
        padding: 1rem;
        border-radius: 0.4rem;
        margin: 1rem 0;
        border: 1px solid #f5c6cb;
      `;
      const wrapper = document.querySelector('.quantity-discount-wrapper');
      if (wrapper) {
        wrapper.appendChild(errorDiv);
      }
    }
    
    errorDiv.textContent = message;
    
    // Auto hide after 5 seconds
    setTimeout(() => {
      if (errorDiv.parentNode) {
        errorDiv.parentNode.removeChild(errorDiv);
      }
    }, 5000);
  }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    new QuantityDiscount();
  });
} else {
  new QuantityDiscount();
}
